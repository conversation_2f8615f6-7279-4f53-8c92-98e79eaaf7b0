import React, { useEffect, useState } from 'react'

const Toast = ({ 
  message, 
  type = 'info', 
  isVisible, 
  duration = 0, // 0 means don't auto-hide
  onClose 
}) => {
  const [show, setShow] = useState(false)

  useEffect(() => {
    if (isVisible) {
      setShow(true)
      if (duration > 0) {
        const timer = setTimeout(() => {
          setShow(false)
          setTimeout(() => onClose && onClose(), 300) // Wait for animation
        }, duration)
        return () => clearTimeout(timer)
      }
    } else {
      setShow(false)
    }
  }, [isVisible, duration, onClose])

  if (!isVisible && !show) return null

  const getIcon = () => {
    switch (type) {
      case 'loading':
        return '⏳'
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'warning':
        return '⚠️'
      default:
        return 'ℹ️'
    }
  }

  return (
    <div className={`toast toast-${type} ${show ? 'toast-show' : 'toast-hide'}`}>
      <div className="toast-content">
        <span className="toast-icon">{getIcon()}</span>
        <span className="toast-message">{message}</span>
        {type === 'loading' && (
          <div className="toast-spinner">
            <div className="spinner"></div>
          </div>
        )}
        {onClose && type !== 'loading' && (
          <button className="toast-close" onClick={() => {
            setShow(false)
            setTimeout(() => onClose(), 300)
          }}>
            ×
          </button>
        )}
      </div>
    </div>
  )
}

export default Toast
