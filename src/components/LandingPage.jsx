import React from 'react'

const LandingPage = ({
  fileInputRef,
  onFileUpload,
  csvInputRef,
  onCSVUpload,
  csvFileName,
  roomNames
}) => {
  return (
    <div className="landing-page">
      <div className="landing-content">
        <div className="landing-header">
          <h1>📄 PDF Annotation Tool</h1>
          <p className="landing-subtitle">
            Upload your PDF files to start creating annotations with room names
          </p>
        </div>

        <div className="landing-main">
          <div className="upload-section">
            <div className="upload-card">
              <div className="upload-icon">📄</div>
              <h2>Upload PDF Files</h2>
              <p>Select one or multiple PDF files to begin annotation</p>
              <button
                className="upload-button primary"
                onClick={() => fileInputRef.current?.click()}
              >
                Choose PDF Files
              </button>
              <p className="upload-hint">
                Supports multiple PDF files • Drag and drop coming soon
              </p>
            </div>

            <div className="divider">
              <span>Optional</span>
            </div>

            {!csvFileName && (
              <div className="upload-card secondary">
                <div className="upload-icon">📊</div>
                <h3>Upload Room Names (CSV)</h3>
                <p>Upload a CSV file with room names for quick annotation</p>
                <button
                  className="upload-button secondary"
                  onClick={() => csvInputRef.current?.click()}
                >
                  Choose CSV File
                </button>
              </div>
            )}
            {csvFileName && (
              <div className="upload-card secondary loaded">
                <div className="upload-icon">✅</div>
                <h3>Room Names Loaded</h3>
                <div className="csv-status">
                  Loaded: {roomNames.length} room names from {csvFileName}
                </div>
              </div>
            )}


          </div>

          <div className="instructions-sidebar">
            <div className="instructions-section">
              <h3>Quick Start</h3>
              <ol className="instructions-list">
                <li>Upload your PDF file(s) using the button on the left</li>
                <li>Optionally upload a CSV file with room names</li>
                <li>Start drawing annotations on your PDF</li>
                <li>Assign room names to each annotation</li>
                <li>Export your work as JSON or annotated PDF</li>
              </ol>
            </div>

            <div className="navbar-instructions">
              <h3>📋 Using the Navigation Bar</h3>
              <p className="navbar-intro">
                Once your PDFs are loaded, you'll see a floating navigation bar at the top with these tools:
              </p>
              <div className="navbar-tools">
                <div className="tool-item">
                  <span className="tool-icon">📊</span>
                  <div>
                    <strong>CSV Upload:</strong> Upload room names for quick annotation
                  </div>
                </div>
                <div className="tool-item">
                  <span className="tool-icon">🎯</span>
                  <div>
                    <strong>Filter Rooms:</strong> Filter room names by hierarchy (if CSV has structure)
                  </div>
                </div>
                <div className="tool-item">
                  <span className="tool-icon">📋</span>
                  <div>
                    <strong>PDF Selector:</strong> Switch between multiple uploaded PDFs
                  </div>
                </div>
                <div className="tool-item">
                  <span className="tool-icon">📄</span>
                  <div>
                    <strong>Page Navigation:</strong> Navigate through PDF pages
                  </div>
                </div>
                <div className="tool-item">
                  <span className="tool-icon">🔍</span>
                  <div>
                    <strong>Zoom Controls:</strong> Zoom in/out (up to 900%) and auto-fit
                  </div>
                </div>
                <div className="tool-item">
                  <span className="tool-icon">✏️</span>
                  <div>
                    <strong>Drawing Tools:</strong> Switch between rectangle and polygon drawing modes
                  </div>
                </div>
                <div className="tool-item">
                  <span className="tool-icon">💾</span>
                  <div>
                    <strong>Export Options:</strong> Export annotations as JSON or annotated PDF
                  </div>
                </div>
              </div>
              <div className="navbar-tip">
                <strong>💡 Pro Tip:</strong> The navigation bar stays at the top and follows you as you work.
                Use keyboard shortcuts and mouse wheel for faster navigation!
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        multiple
        onChange={onFileUpload}
        style={{ display: 'none' }}
      />
      <input
        ref={csvInputRef}
        type="file"
        accept=".csv"
        onChange={onCSVUpload}
        style={{ display: 'none' }}
      />
    </div>
  )
}

export default LandingPage
